import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserConvertCustomer, UserOrder, UserConvert } from '@modules/business/entities';
import { PaginatedResult } from '@common/response'; // Using user's simpler path
import { QueryUserConvertCustomerDto as QueryUserConvertCustomerDtoUser } from '@modules/business/user/dto'; // Aliased DTO
import { QueryUserConvertCustomerDto as QueryUserConvertCustomerDtoAdmin } from '@modules/business/admin/dto'; // Aliased DTO
import { Agent } from '@modules/agent/entities/agent.entity'; // From user file
import { User } from '@modules/user/entities/user.entity'; // From user file
import { AgentUser } from '@modules/agent/entities';
import { SearchHelper } from '@common/helpers';

/**
 * Repository xử lý các thao tác với bảng user_convert_customers,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class UserConvertCustomerRepository extends Repository<UserConvertCustomer> {
  private readonly logger = new Logger(UserConvertCustomerRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserConvertCustomer, dataSource.createEntityManager());
  }

  // --- Base Query Builders ---

  /**
   * Tạo truy vấn cơ bản cho bảng user_convert_customers (User context version)
   * @returns QueryBuilder cho bảng user_convert_customers
   */
  private createBaseQuery_user(): SelectQueryBuilder<UserConvertCustomer> {
    return this.createQueryBuilder('customer');
  }

  // --- Create Methods ---

  /**
   * Tạo khách hàng chuyển đổi mới (User context method)
   * @param customerData Dữ liệu khách hàng chuyển đổi
   * @returns Khách hàng chuyển đổi đã được tạo
   */
  async createUserConvertCustomer(customerData: Partial<UserConvertCustomer>): Promise<UserConvertCustomer> {
    try {
      this.logger.log(`Tạo khách hàng chuyển đổi mới với phone: ${customerData.phone}`);

      const customer = this.create(customerData);
      const savedCustomer = await this.save(customer);

      this.logger.log(`Đã tạo khách hàng chuyển đổi với ID: ${savedCustomer.id}`);
      return savedCustomer;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo khách hàng chuyển đổi: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo khách hàng chuyển đổi: ${error.message}`);
    }
  }

  /**
   * Tạo nhiều khách hàng chuyển đổi cùng lúc (User context method)
   * @param customersData Mảng dữ liệu khách hàng chuyển đổi
   * @returns Mảng khách hàng chuyển đổi đã được tạo
   */
  async createBulkUserConvertCustomers(customersData: Partial<UserConvertCustomer>[]): Promise<UserConvertCustomer[]> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Bắt đầu tạo ${customersData.length} khách hàng chuyển đổi`);

      const createdCustomers: UserConvertCustomer[] = [];

      for (const customerData of customersData) {
        const customer = queryRunner.manager.create(UserConvertCustomer, customerData);
        const savedCustomer = await queryRunner.manager.save(customer);
        createdCustomers.push(savedCustomer);
      }

      await queryRunner.commitTransaction();

      this.logger.log(`Đã tạo thành công ${createdCustomers.length} khách hàng chuyển đổi`);
      return createdCustomers;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi tạo bulk khách hàng chuyển đổi: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo bulk khách hàng chuyển đổi: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Kiểm tra danh sách số điện thoại đã tồn tại
   * @param phones Mảng số điện thoại cần kiểm tra
   * @returns Mảng số điện thoại đã tồn tại
   */
  async findExistingPhones(phones: string[]): Promise<string[]> {
    try {
      if (phones.length === 0) return [];

      const existingCustomers = await this.createBaseQuery_user()
        .select(['customer.phone'])
        .where('customer.phone IN (:...phones)', { phones })
        .getMany();

      return existingCustomers.map(customer => customer.phone);
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra số điện thoại tồn tại: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi kiểm tra số điện thoại tồn tại: ${error.message}`);
    }
  }

  // --- Update Methods ---

  /**
   * Cập nhật thông tin khách hàng chuyển đổi (User context method)
   * @param id ID khách hàng chuyển đổi
   * @param updateData Dữ liệu cập nhật
   * @returns Khách hàng chuyển đổi đã được cập nhật
   */
  async updateUserConvertCustomer(id: number, updateData: Partial<UserConvertCustomer>): Promise<UserConvertCustomer> {
    try {
      this.logger.log(`Cập nhật khách hàng chuyển đổi với ID: ${id}`);

      // Thêm thời gian cập nhật
      const dataWithTimestamp: any = {
        ...updateData,
        updatedAt: Date.now(),
      };

      await this.update(id, dataWithTimestamp);
      const updatedCustomer = await this.findById(id);

      if (!updatedCustomer) {
        throw new Error(`Không tìm thấy khách hàng chuyển đổi với ID: ${id} sau khi cập nhật`);
      }

      this.logger.log(`Đã cập nhật khách hàng chuyển đổi với ID: ${id}`);
      return updatedCustomer;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật khách hàng chuyển đổi với ID ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi cập nhật khách hàng chuyển đổi với ID ${id}: ${error.message}`);
    }
  }

  /**
   * Merge hai khách hàng chuyển đổi (User context method)
   * @param sourceId ID khách hàng nguồn (sẽ bị xóa)
   * @param targetId ID khách hàng đích (sẽ được cập nhật)
   * @param mergedData Dữ liệu sau merge
   * @returns Khách hàng chuyển đổi đã được merge
   */
  async mergeUserConvertCustomers(
    sourceId: number,
    targetId: number,
    mergedData: Partial<UserConvertCustomer>
  ): Promise<UserConvertCustomer> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Bắt đầu merge khách hàng chuyển đổi: source=${sourceId}, target=${targetId}`);

      // Bước 1: Cập nhật tất cả foreign key references từ source sang target
      this.logger.log(`Cập nhật foreign key references từ source=${sourceId} sang target=${targetId}`);

      // Cập nhật user_orders
      await queryRunner.manager.update(
        UserOrder,
        { userConvertCustomerId: sourceId },
        { userConvertCustomerId: targetId }
      );

      // Cập nhật user_converts
      await queryRunner.manager.update(
        UserConvert,
        { convertCustomerId: sourceId },
        { convertCustomerId: targetId }
      );

      // Cập nhật customer_facebook (nếu có)
      await queryRunner.manager.query(
        'UPDATE customer_facebook SET user_convert_customer_id = $1 WHERE user_convert_customer_id = $2',
        [targetId, sourceId]
      );

      // Bước 2: Cập nhật khách hàng đích với dữ liệu merge
      const updateData: any = {
        ...mergedData,
        updatedAt: Date.now(),
      };

      await queryRunner.manager.update(UserConvertCustomer, targetId, updateData);

      // Bước 3: Xóa khách hàng nguồn (bây giờ an toàn vì không còn foreign key references)
      await queryRunner.manager.delete(UserConvertCustomer, sourceId);

      // Commit transaction
      await queryRunner.commitTransaction();

      // Lấy khách hàng đã được merge
      const mergedCustomer = await this.findById(targetId);

      if (!mergedCustomer) {
        throw new Error(`Không thể tìm thấy khách hàng sau khi merge với ID: ${targetId}`);
      }

      this.logger.log(`Đã merge thành công khách hàng chuyển đổi với ID: ${targetId}`);
      return mergedCustomer;
    } catch (error) {
      // Rollback transaction nếu có lỗi
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi merge khách hàng chuyển đổi: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi merge khách hàng chuyển đổi: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Tìm khách hàng theo số điện thoại (User context method)
   * @param phone Số điện thoại
   * @returns Khách hàng hoặc null nếu không tìm thấy
   */
  async findByPhone(phone: string): Promise<UserConvertCustomer | null> {
    try {
      this.logger.log(`Tìm khách hàng theo số điện thoại: ${phone}`);

      const customer = await this.createBaseQuery_user()
        .where('customer.phone = :phone', { phone })
        .getOne();

      this.logger.log(customer
        ? `Đã tìm thấy khách hàng với phone: ${phone}`
        : `Không tìm thấy khách hàng với phone: ${phone}`
      );

      return customer;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm khách hàng theo phone: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm khách hàng theo phone: ${error.message}`);
    }
  }

  /**
   * Tạo query builder cơ bản cho bảng user_convert_customers (Admin context version)
   * @returns SelectQueryBuilder<UserConvertCustomer>
   */
  private createBaseQuery_admin(): SelectQueryBuilder<UserConvertCustomer> {
    this.logger.log('(Admin) createBaseQuery: Tạo query builder cơ bản cho bảng user_convert_customers');
    return this.createQueryBuilder('customer');
  }

  /**
   * Tạo query builder cơ bản với join bảng user_orders (Admin context - unique method)
   * @returns SelectQueryBuilder<UserConvertCustomer>
   */
  private createBaseQueryWithOrders_admin(): SelectQueryBuilder<UserConvertCustomer> {
    this.logger.log('(Admin) createBaseQueryWithOrders: Tạo query builder với join bảng user_orders');
    return this.createQueryBuilder('customer')
      .leftJoinAndSelect('customer.userOrders', 'orders'); // Assuming 'userOrders' is a defined relation on UserConvertCustomer
  }


  // --- Methods from User File ---

  /**
   * Tìm khách hàng theo ID với thông tin Agent và User (User context method)
   * @param id ID khách hàng
   * @returns Khách hàng hoặc null nếu không tìm thấy, augmented with Agent and User
   */
  async findById(id: number): Promise<UserConvertCustomer & { agent?: Agent, user?: User } | null> {
    try {
      const query = this.createBaseQuery_user() // Use user base query
        .where('customer.id = :id', { id });

      const customer = await query.getOne();

      if (!customer) {
        return null;
      }

      const result: UserConvertCustomer & { agent?: Agent, user?: User } = { ...customer };

      if (customer.userId) {
        const user = await this.dataSource
          .getRepository(User)
          .createQueryBuilder('user')
          .select(['user.id', 'user.fullName', 'user.email']) // Example: select only necessary fields
          .where('user.id = :userId', { userId: customer.userId })
          .getOne();
        if (user) {
          result.user = user;
        }
      }

      if (customer.agentId) {
        // Ensure Agent entity is correctly imported and repository is accessible
        const agent = await this.dataSource
          .getRepository(Agent)
          .createQueryBuilder('agent')
          .select(['agent.id', 'agent.name']) // Example: select only necessary fields
          .where('agent.id = :agentId', { agentId: customer.agentId })
          .getOne();
        if (agent) {
          result.agent = agent;
        }
      }
      return result;
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tìm khách hàng theo ID ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm khách hàng theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm danh sách khách hàng với phân trang (User context method)
   * @param userId ID người dùng (specific to user context filtering)
   * @param queryDto Tham số truy vấn (User DTO)
   * @returns Danh sách khách hàng với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserConvertCustomerDtoUser): Promise<PaginatedResult<UserConvertCustomer>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        platform,
        agentId, // User DTO has agentId
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;

      const skip = (page - 1) * limit;

      const query = this.createBaseQuery_user() // Use user base query
        .where('customer.userId = :userId', { userId }); // User-specific mandatory filter

      if (search) {
        // Sử dụng SearchHelper với PostgreSQL translate function để tìm kiếm không dấu
        const searchCondition = SearchHelper.createPostgreSQLSearchCondition(
          search,
          ['name', 'phone', 'address'],
          'customer'
        );

        if (searchCondition.whereClause) {
          query.andWhere(searchCondition.whereClause, searchCondition.parameters);
        }
      }
      if (platform) {
        query.andWhere('customer.platform ILIKE :platform', { platform });
      }
      if (agentId) {
        query.andWhere('customer.agentId = :agentId', { agentId });
      }

      query.orderBy(`customer.${sortBy}`, sortDirection);
      query.skip(skip).take(limit);

      const [items, total] = await query.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`(User) Lỗi khi lấy danh sách khách hàng: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy danh sách khách hàng: ${error.message}`);
    }
  }

  /**
   * Kiểm tra agent có tồn tại và thuộc về user không
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns true nếu agent tồn tại và thuộc về user, false nếu không
   */
  async checkAgentExists(agentId: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Kiểm tra agent ${agentId} có thuộc về user ${userId} không`);

      // Kiểm tra trong bảng agents_user (bảng liên kết agent với user)
      const agentUser = await this.dataSource
        .getRepository(AgentUser)
        .createQueryBuilder('agentUser')
        .select(['agentUser.id'])
        .where('agentUser.id = :agentId', { agentId })
        .andWhere('agentUser.userId = :userId', { userId })
        .getOne();

      const exists = !!agentUser;
      this.logger.log(`Agent ${agentId} ${exists ? 'thuộc về' : 'không thuộc về'} user ${userId}`);

      return exists;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra agent ${agentId} cho user ${userId}: ${error.message}`, error.stack);
      return false;
    }
  }

  // --- Methods from Admin File ---

  /**
   * Tìm khách hàng chuyển đổi theo ID (Admin context method)
   * @param id ID của khách hàng chuyển đổi
   * @returns Khách hàng chuyển đổi hoặc null nếu không tìm thấy
   */
  async findUserConvertCustomerById(id: number): Promise<UserConvertCustomer | null> {
    this.logger.log(`(Admin) findUserConvertCustomerById: Tìm khách hàng chuyển đổi với ID: ${id}`);
    try {
      const result = await this.createBaseQuery_admin() // Use admin base query
        .where('customer.id = :id', { id })
        .getOne();
      this.logger.log(result
        ? `(Admin) findUserConvertCustomerById: Đã tìm thấy khách hàng chuyển đổi với ID: ${id}`
        : `(Admin) findUserConvertCustomerById: Không tìm thấy khách hàng chuyển đổi với ID: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(`(Admin) findUserConvertCustomerById: Lỗi khi tìm khách hàng chuyển đổi với ID ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm khách hàng chuyển đổi theo ID kèm theo đơn hàng (Admin context method)
   * @param id ID của khách hàng chuyển đổi
   * @returns Khách hàng chuyển đổi với đơn hàng hoặc null nếu không tìm thấy
   */
  async findUserConvertCustomerByIdWithOrders(id: number): Promise<UserConvertCustomer | null> {
    this.logger.log(`(Admin) findUserConvertCustomerByIdWithOrders: Tìm khách hàng chuyển đổi với ID: ${id} kèm đơn hàng`);
    try {
      const result = await this.createBaseQueryWithOrders_admin() // Use admin specific base query
        .where('customer.id = :id', { id })
        .getOne();
      this.logger.log(result
        ? `(Admin) findUserConvertCustomerByIdWithOrders: Đã tìm thấy khách hàng chuyển đổi với ID: ${id} kèm đơn hàng`
        : `(Admin) findUserConvertCustomerByIdWithOrders: Không tìm thấy khách hàng chuyển đổi với ID: ${id}`);
      return result;
    } catch (error) {
      this.logger.error(`(Admin) findUserConvertCustomerByIdWithOrders: Lỗi khi tìm khách hàng chuyển đổi với ID ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm danh sách khách hàng chuyển đổi với phân trang, tìm kiếm và lọc (Admin context method)
   * @param queryParams Tham số truy vấn (Admin DTO)
   * @returns Danh sách khách hàng chuyển đổi phân trang
   */
  async findUserConvertCustomers(queryParams: QueryUserConvertCustomerDtoAdmin): Promise<PaginatedResult<UserConvertCustomer>> {
    this.logger.log(`(Admin) findUserConvertCustomers: Tìm kiếm khách hàng chuyển đổi với các tham số: ${JSON.stringify(queryParams)}`);
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        userId, // Admin DTO can filter by userId
        agentId, // Admin DTO can filter by agentId
        platform, // Admin DTO can filter by platform
        createdAtFrom,
        createdAtTo
      } = queryParams;

      const skip = (page - 1) * limit;
      this.logger.log(`(Admin) findUserConvertCustomers: Phân trang: skip ${skip}, limit ${limit}`);

      const queryBuilder = this.createBaseQuery_admin(); // Use admin base query
      this.logger.log(`(Admin) findUserConvertCustomers: Đã tạo query builder cơ bản`);

      if (search) {
        // Sử dụng SearchHelper với PostgreSQL translate function để tìm kiếm không dấu
        const searchCondition = SearchHelper.createPostgreSQLSearchCondition(
          search,
          ['name', 'phone', 'address'],
          'customer'
        );

        if (searchCondition.whereClause) {
          queryBuilder.andWhere(searchCondition.whereClause, searchCondition.parameters);
        }
      }
      if (userId) {
        queryBuilder.andWhere('customer.userId = :userId', { userId });
      }
      if (agentId) {
        queryBuilder.andWhere('customer.agentId = :agentId', { agentId });
      }
      if (platform) {
        queryBuilder.andWhere('customer.platform ILIKE :platform', { platform });
      }
      if (createdAtFrom) {
        queryBuilder.andWhere('customer.createdAt >= :createdAtFrom', { createdAtFrom: Number(createdAtFrom) }); // Sử dụng số thay vì Date object
      }
      if (createdAtTo) {
        queryBuilder.andWhere('customer.createdAt <= :createdAtTo', { createdAtTo: Number(createdAtTo) }); // Sử dụng số thay vì Date object
      }

      this.logger.log(`(Admin) findUserConvertCustomers: Đang đếm tổng số bản ghi`);
      const totalItems = await queryBuilder.getCount();
      this.logger.log(`(Admin) findUserConvertCustomers: Tổng số bản ghi: ${totalItems}`);

      queryBuilder.orderBy(`customer.${sortBy}`, sortDirection).skip(skip).take(limit);

      this.logger.log(`(Admin) findUserConvertCustomers: Đang thực hiện truy vấn`);
      const items = await queryBuilder.getMany();
      this.logger.log(`(Admin) findUserConvertCustomers: Đã tìm thấy ${items.length}/${totalItems} khách hàng chuyển đổi`);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`(Admin) findUserConvertCustomers: Lỗi khi tìm kiếm khách hàng chuyển đổi: ${error.message}`);
      throw error;
    }
  }
}